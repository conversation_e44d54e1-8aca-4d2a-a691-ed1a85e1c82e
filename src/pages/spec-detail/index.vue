<route lang="jsonc" type="page">
{
  "layout": "default",
  "style": {
    "navigationBarTitleText": "证件照详情",
    "navigationBarBackgroundColor": "#ffffff",
    "navigationBarTextStyle": "black"
  }
}
</route>

<script lang="ts" setup>
import type { IPhotoItem } from '@/api/photo'

// 接收页面参数
const data = ref<IPhotoItem | null>(null)

onLoad((options) => {
  if (options?.data) {
    try {
      data.value = JSON.parse(decodeURIComponent(options.data))
      console.log('详情页数据:', data.value)
    } catch (error) {
      console.error('解析详情数据失败:', error)
      uni.showToast({
        title: '数据解析失败',
        icon: 'error'
      })
    }
  }
})

// 开始制作证件照
function startMaking() {
  if (!data.value) return
  
  console.log('开始制作证件照:', data.value)
  uni.showToast({
    title: '功能开发中...',
    icon: 'none'
  })
}
</script>

<template>
  <view class="detail-container">
    <view v-if="data" class="content">
      <!-- 证件照信息卡片 -->
      <view class="info-card">
        <view class="card-header">
          <view class="photo-name">{{ data.name }}</view>
          <view class="photo-title">{{ data.title }}</view>
        </view>
        
        <view class="card-body">
          <view class="info-row">
            <view class="label">尺寸规格</view>
            <view class="value">{{ data.widthMm }} × {{ data.heightMm }} mm</view>
          </view>
          
          <view class="info-row">
            <view class="label">像素大小</view>
            <view class="value">{{ data.widthPx }} × {{ data.heightPx }} px</view>
          </view>
          
          <view class="info-row">
            <view class="label">详细说明</view>
            <view class="value">{{ data.detail || '暂无详细说明' }}</view>
          </view>
        </view>
      </view>

      <!-- 示例图片区域 -->
      <view class="example-section">
        <view class="section-title">示例效果</view>
        <view class="example-image">
          <image 
            src="/static/icon/camera.png" 
            mode="aspectFit"
            class="placeholder-image"
          />
          <view class="placeholder-text">证件照示例</view>
        </view>
      </view>

      <!-- 操作按钮 -->
      <view class="action-section">
        <button 
          type="primary" 
          class="make-button"
          @click="startMaking"
        >
          开始制作
        </button>
      </view>
    </view>

    <!-- 加载状态 -->
    <view v-else class="loading-container">
      <view class="loading-text">加载中...</view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.detail-container {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding: 20rpx;
}

.content {
  display: flex;
  flex-direction: column;
  gap: 30rpx;
}

.info-card {
  background: white;
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

.card-header {
  border-bottom: 2rpx solid #f0f0f0;
  padding-bottom: 20rpx;
  margin-bottom: 20rpx;
}

.photo-name {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
}

.photo-title {
  font-size: 28rpx;
  color: #666;
}

.card-body {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.info-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15rpx 0;
}

.label {
  font-size: 28rpx;
  color: #666;
  font-weight: 500;
}

.value {
  font-size: 28rpx;
  color: #333;
  font-weight: 600;
}

.example-section {
  background: white;
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.example-image {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 400rpx;
  background: #f8f8f8;
  border-radius: 15rpx;
  border: 2rpx dashed #ddd;
}

.placeholder-image {
  width: 100rpx;
  height: 100rpx;
  opacity: 0.5;
}

.placeholder-text {
  font-size: 24rpx;
  color: #999;
  margin-top: 20rpx;
}

.action-section {
  padding: 20rpx 0;
}

.make-button {
  width: 100%;
  height: 88rpx;
  background: linear-gradient(135deg, #4E47FD 0%, #6B5FFF 100%);
  border-radius: 44rpx;
  font-size: 32rpx;
  font-weight: bold;
  color: white;
  border: none;
  box-shadow: 0 8rpx 24rpx rgba(78, 71, 253, 0.3);
}

.make-button:active {
  transform: translateY(2rpx);
  box-shadow: 0 4rpx 12rpx rgba(78, 71, 253, 0.3);
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 400rpx;
}

.loading-text {
  font-size: 28rpx;
  color: #666;
}
</style>
