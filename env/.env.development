# 变量必须以 VITE_ 为前缀才能暴露给外部读取
NODE_ENV = 'development'
# 是否去除console 和 debugger
VITE_DELETE_CONSOLE = false
# 是否开启sourcemap
VITE_SHOW_SOURCEMAP = true

# 应用配置
VITE_APP_TITLE = '证件照制作'
VITE_APP_PORT = 9000
VITE_UNI_APPID = '__UNI__XXXXXX'
VITE_WX_APPID = 'wx1234567890'
VITE_FALLBACK_LOCALE = 'zh-Hans'

# API配置
VITE_API_BASE_URL = 'https://your-api-domain.com/api'
VITE_SERVER_BASEURL = 'https://your-api-domain.com/api'
VITE_API_SECONDARY_URL = 'https://your-secondary-api-domain.com/api'
VITE_UPLOAD_BASEURL = 'https://your-api-domain.com/api/upload'

# H5代理配置
VITE_APP_PROXY = true
VITE_APP_PROXY_PREFIX = '/api'
VITE_APP_PUBLIC_BASE = '/'

# 登录页面路径
VITE_LOGIN_URL = '/pages/login/login'
